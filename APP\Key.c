#include ".\HEAD\Key.h"

// 初始化按键
void Key_Init(void)
{
    // 设置按键引脚为输入模式
    KEY1_PIN = 1;  // 设置为高电平（上拉）
}

// 按键扫描函数（带消抖）
unsigned char Key_Scan(void)
{
    if (KEY1_PIN == 0)  // 按键按下（低电平有效）
    {
        // 消抖延时
        delay_ms(20);
        if (KEY1_PIN == 0)  // 确认按键按下
        {
            while (KEY1_PIN == 0);  // 等待按键释放
            return KEY1_PRESS;
        }
    }
    return KEY_NONE;
}

// 简单的延时函数
void delay_ms(unsigned int ms)
{
    unsigned int i, j;
    for (i = 0; i < ms; i++)
        for (j = 0; j < 120; j++);  // 根据您的时钟频率调整此值
}
