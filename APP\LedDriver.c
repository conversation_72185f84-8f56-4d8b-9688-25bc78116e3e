/*==============================================================================
            Copyright(C) 2021-2029.  XXXXXX Tech. Co., Ltd.
--------------------------------------------------------------------------------
Project No.   : XXXXXX
File Name     : LEDDRIVER..C	
Description   : 
              ；    
              ；   
Notice        :
             1:
             2:

Author        :  
Start Date    :  
Release Date  :  
Approve Date  :
Version       : V1.0
CheckSum      : XXXXXXX
Function List :
             1:
             2:

RevisionHistory:
Rev#  CheckSum   Date     Author     Comments(Function+Date)
-----+--------+----------+---------+--------------------------------------------
0.0 2021/10/08 Author Just build the function
0.1
==============================================================================*/

//-------------------------------------------------------------------------------
#ifndef  LEDDRIVER_C
	#define  LEDDRIVER_C
#endif
//-------------------------------------------------------------------------------

//---------------



#include	".\head\Config.h"



/****************************************************************************
 * Function Description:InitAllLedDriverRam process
 * Input parameter     :void
 * Output paramter     :void
 ****************************************************************************/
void InitAllLedDriverRam(void)
{
		LedWorkGroupStr.LedWorkMode   = 0x00;	
	
	
//	LedWorkGroupStr.Led1FastFlashCnt = 0x00;	
//	LedWorkGroupStr.Led1SlowFlashCnt = 0x00;	
//	LedWorkGroupStr.Led1FlashMode = 0x00;


//	LedWorkGroupStr.Led1FlashFlag = 0x00;	
//	LedWorkGroupStr.Led2FlashFlag = 0x00;
//	LedWorkGroupStr.Led3FlashFlag = 0x00;
//	LedWorkGroupStr.Led4FlashFlag = 0x00;
//	LedWorkGroupStr.Led5FlashFlag = 0x00;
//	LedWorkGroupStr.Led6FlashFlag = 0x00;
}

/****************************************************************************
 * Function Description:InitAllLedOff process
 * Input parameter     :void
 * Output paramter     :void
 ****************************************************************************/
void InitAllLedOff(void)
{
		LED1_WorkOff();
		LED2_WorkOff();
		LED3_WorkOff();
		LED4_WorkOff();
		LED5_WorkOff();
		LED6_WorkOff();	
}


/****************************************************************************
 * Function Description:LED1_WorkOn process
 * Input parameter     :void
 * Output paramter     :void
 ****************************************************************************/
void LED1_WorkOn(void)
{
		LED1_IoOutSet(); 	
		LED1_L();
}

/****************************************************************************
 * Function Description:LED1_WorkOff process
 * Input parameter     :void
 * Output paramter     :void
 ****************************************************************************/
void LED1_WorkOff(void)
{
		LED1_IoOutSet(); 
		LED1_H();	
}

/****************************************************************************
 * Function Description:LED1_WorkFlash process
 * Input parameter     :void
 * Output paramter     :void
 ****************************************************************************/
//void LED1_WorkFlash(void)
//{
//		LED1_IoOutSet(); 
//		if(LedWorkGroupStr.Led1FlashFlag == 0x01)
//		{
//				LedWorkGroupStr.Led1FlashFlag = 0x00;
//				LED1_H();			
//		}
//		else
//		{
//				LedWorkGroupStr.Led1FlashFlag = 0x01;
//				LED1_L();			
//		}
//}


/****************************************************************************
 * Function Description:LED2_WorkOn process
 * Input parameter     :void
 * Output paramter     :void
 ****************************************************************************/
void LED2_WorkOn(void)
{
		LED2_IoOutSet(); 	
		LED2_L();
}

/****************************************************************************
 * Function Description:LED2_WorkOff process
 * Input parameter     :void
 * Output paramter     :void
 ****************************************************************************/
void LED2_WorkOff(void)
{
		LED2_IoOutSet(); 
		LED2_H();	
}

/****************************************************************************
 * Function Description:LED2_WorkFlash process
 * Input parameter     :void
 * Output paramter     :void
 ****************************************************************************/
//void LED2_WorkFlash(void)
//{
//		LED2_IoOutSet(); 
//		if(LedWorkGroupStr.Led2FlashFlag == 0x01)
//		{
//				LedWorkGroupStr.Led2FlashFlag = 0x00;
//				LED2_H();			
//		}
//		else
//		{
//				LedWorkGroupStr.Led2FlashFlag = 0x01;
//				LED2_L();			
//		}
//}



/****************************************************************************
 * Function Description:LED3_WorkOn process
 * Input parameter     :void
 * Output paramter     :void
 ****************************************************************************/
void LED3_WorkOn(void)
{
		LED3_IoOutSet(); 	
		LED3_L();
}

/****************************************************************************
 * Function Description:LED3_WorkOff process
 * Input parameter     :void
 * Output paramter     :void
 ****************************************************************************/
void LED3_WorkOff(void)
{
		LED3_IoOutSet(); 
		LED3_H();	
}

/****************************************************************************
 * Function Description:LED3_WorkFlash process
 * Input parameter     :void
 * Output paramter     :void
 ****************************************************************************/
//void LED3_WorkFlash(void)
//{
//		LED3_IoOutSet(); 
//		if(LedWorkGroupStr.Led3FlashFlag == 0x01)
//		{
//				LedWorkGroupStr.Led3FlashFlag = 0x00;
//				LED3_H();			
//		}
//		else
//		{
//				LedWorkGroupStr.Led3FlashFlag = 0x01;
//				LED3_L();			
//		}
//}


/****************************************************************************
 * Function Description:LED4_WorkOn process
 * Input parameter     :void
 * Output paramter     :void
 ****************************************************************************/
void LED4_WorkOn(void)
{
		LED4_IoOutSet(); 	
		LED4_L();
}

/****************************************************************************
 * Function Description:LED4_WorkOff process
 * Input parameter     :void
 * Output paramter     :void
 ****************************************************************************/
void LED4_WorkOff(void)
{
		LED4_IoOutSet(); 
		LED4_H();	
}

/****************************************************************************
 * Function Description:LED4_WorkFlash process
 * Input parameter     :void
 * Output paramter     :void
 ****************************************************************************/
//void LED4_WorkFlash(void)
//{
//		LED4_IoOutSet(); 
//		if(LedWorkGroupStr.Led4FlashFlag == 0x01)
//		{
//				LedWorkGroupStr.Led4FlashFlag = 0x00;
//				LED4_H();			
//		}
//		else
//		{
//				LedWorkGroupStr.Led4FlashFlag = 0x01;
//				LED4_L();			
//		}
//}



/****************************************************************************
 * Function Description:LED5_WorkOn process
 * Input parameter     :void
 * Output paramter     :void
 ****************************************************************************/
void LED5_WorkOn(void)
{
		LED5_IoOutSet(); 	
		LED5_L();
}

/****************************************************************************
 * Function Description:LED5_WorkOff process
 * Input parameter     :void
 * Output paramter     :void
 ****************************************************************************/
void LED5_WorkOff(void)
{
		LED5_IoOutSet(); 
		LED5_H();	
}

/****************************************************************************
 * Function Description:LED5_WorkFlash process
 * Input parameter     :void
 * Output paramter     :void
 ****************************************************************************/
//void LED5_WorkFlash(void)
//{
//		LED5_IoOutSet(); 
//		if(LedWorkGroupStr.Led5FlashFlag == 0x01)
//		{
//				LedWorkGroupStr.Led5FlashFlag = 0x00;
//				LED5_H();			
//		}
//		else
//		{
//				LedWorkGroupStr.Led5FlashFlag = 0x01;
//				LED5_L();			
//		}
//}


/****************************************************************************
 * Function Description:LED6_WorkOn process
 * Input parameter     :void
 * Output paramter     :void
 ****************************************************************************/
void LED6_WorkOn(void)
{
		LED6_IoOutSet(); 	
		LED6_L();
}

/****************************************************************************
 * Function Description:LED6_WorkOff process
 * Input parameter     :void
 * Output paramter     :void
 ****************************************************************************/
void LED6_WorkOff(void)
{
		LED6_IoOutSet(); 
		LED6_H();	
}

/****************************************************************************
 * Function Description:LED6_WorkFlash process
 * Input parameter     :void
 * Output paramter     :void
 ****************************************************************************/
//void LED6_WorkFlash(void)
//{
//		LED6_IoOutSet(); 
//		if(LedWorkGroupStr.Led6FlashFlag == 0x01)
//		{
//				LedWorkGroupStr.Led6FlashFlag = 0x00;
//				LED6_H();			
//		}
//		else
//		{
//				LedWorkGroupStr.Led6FlashFlag = 0x01;
//				LED6_L();			
//		}
//}
