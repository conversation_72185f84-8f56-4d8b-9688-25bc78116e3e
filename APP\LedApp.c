/*==============================================================================
            Copyright(C) 2021-2029.  XXXXXX Tech. Co., Ltd.
--------------------------------------------------------------------------------
Project No.   : XXXXXX
File Name     : LEDAPP..C	
Description   : 
              ；    
              ；   
Notice        :
             1:
             2:

Author        :  
Start Date    :  
Release Date  :  
Approve Date  :
Version       : V1.0
CheckSum      : XXXXXXX
Function List :
             1:
             2:

RevisionHistory:
Rev#  CheckSum   Date     Author     Comments(Function+Date)
-----+--------+----------+---------+--------------------------------------------
0.0 2021/10/08 Author Just build the function
0.1
==============================================================================*/

//-------------------------------------------------------------------------------
#ifndef  LEDAPP_C
	#define  LEDAPP_C
#endif
//-------------------------------------------------------------------------------

//---------------




#include	".\head\Config.h"

// 在文件开头定义
TimeFlagType TimeFlagStr;

/****************************************************************************
 * Function Description:InitAllLedRamApp process
 * Input parameter     :void
 * Output paramter     :void
 ****************************************************************************/
void InitAllLedRamApp(void)
{
    InitAllLedDriverRam();
    TimeFlagStr.Task10msFlag = 0x01;  // 初始化为可执行状态
}

/****************************************************************************
 * Function Description:LedApp process
 * Input parameter     :void
 * Output paramter     :void
 ****************************************************************************/
void LedApp(void)
{
		if(TimeFlagStr.Task10msFlag == 0x01)
		{
				TimeFlagStr.Task10msFlag = 0x00;	

				switch(LedWorkGroupStr.LedWorkMode)    //系统工作状态指示：红灯亮时正常，红灯闪异常
				{
						case D_SPEED_LEVEL_1:			
								InitAllLedOff();								
								LED1_WorkOn();	
						break;	
								
						case D_SPEED_LEVEL_2:	
								InitAllLedOff();								
								LED1_WorkOn();								
								LED2_WorkOn();		
						break;								
								
						case D_SPEED_LEVEL_3:	
								InitAllLedOff();								
								LED1_WorkOn();								
								LED2_WorkOn();							
								LED3_WorkOn();										
						break;
						
						case D_SPEED_LEVEL_4:						 	
								InitAllLedOff();								
								LED1_WorkOn();								
								LED2_WorkOn();							
								LED3_WorkOn();
								LED4_WorkOn();						
						break;
						
						case D_SPEED_LEVEL_5:											 	
								InitAllLedOff();								
								LED1_WorkOn();								
								LED2_WorkOn();							
								LED3_WorkOn();
								LED4_WorkOn();
								LED5_WorkOn();						
						break;				

						case D_SPEED_LEVEL_6:											 	
								InitAllLedOff();								
								LED1_WorkOn();								
								LED2_WorkOn();							
								LED3_WorkOn();
								LED4_WorkOn();
								LED5_WorkOn();	
								LED6_WorkOn();						
						break;	
						
						default:	
								InitAllLedOff();				
						break;					
				}			
		}
}

// 在某个定时器中断中更新10ms标志
// 例如在定时器中断函数中：
void Timer0_ISR(void) interrupt 1
{
    static unsigned char ms_count = 0;
    
    // 假设定时器每1ms中断一次
    if (++ms_count >= 10)
    {
        ms_count = 0;
        TimeFlagStr.Task10msFlag = 0x01;  // 设置10ms任务标志
    }
}

