// PWM.c
#include ".\HEAD\PWM.h"

// 初始化 PWM（基于 P3.7 输出）
void PWM_Init(void)
{
    // 清除 PWM 控制相关位
    PWMCON &= ~((1 << 6) | (1 << 5) | (1 << 4)); // 清除高电平/分频选择
    PWMCON &= ~(1 << 0);                        // 关闭 P3.7 输出

    // 设置 PWM 工作模式
    PWMCON |= (1 << 7);         // 允许 PWM
    PWMCON &= ~(1 << 6);        // 高电平有效
    PWMCON |= (3 << 4);         // 分频设置为 /16
    PWMCON |= (1 << 0);         // 开启 P3.7 作为 PWM 输出

    PWMP = PWM_PERIOD_CNT;      // 设置周期
    PWMD = PWM_DUTY_CNT;        // 设置初始占空比
}

// 动态修改占空比（0 ~ PWM_PERIOD_CNT）
void PWM_SetDuty(unsigned int duty)
{
    if (duty > PWM_PERIOD_CNT)
        duty = PWM_PERIOD_CNT;
    PWMD = duty;
}

// 按百分比设置占空比（0~100）
void PWM_SetDutyPercent(unsigned char pct)
{
		unsigned int val;
    if (pct > 100) pct = 100;
    // 计算出对应的寄存器值
    val = (PWM_PERIOD_CNT * pct) / 100;
    PWM_SetDuty(val);
}


// 启用 PWM 输出（P3.7）
void PWM_Enable(void)
{
    PWMCON |= (1 << 0);
}

// 禁用 PWM 输出（P3.7）
void PWM_Disable(void)
{
    PWMCON &= ~(1 << 0);
}
