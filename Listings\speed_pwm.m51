<PERSON>L<PERSON> BANKED LINKER/LOCATER V6.22.2.0                                                    06/17/2025  08:59:36  PAGE 1


BL51 BANKED LINKER/LOCATER V6.22.2.0, INVOKED BY:
C:\KEIL_V5\C51\BIN\BL51.EXE .\Objects\main.obj, .\Objects\PWM.obj, .\Objects\Key.obj TO .\Objects\speed_pwm PRINT (.\Lis
>> tings\speed_pwm.m51) RAMSIZE (256)


MEMORY MODEL: SMALL


INPUT MODULES INCLUDED:
  .\Objects\main.obj (MAIN)
  .\Objects\PWM.obj (PWM)
  .\Objects\Key.obj (KEY)
  C:\KEIL_V5\C51\LIB\C51S.LIB (?C_STARTUP)
  C:\KEIL_V5\C51\LIB\C51S.LIB (?C?IMUL)
  C:\KEIL_V5\C51\LIB\C51S.LIB (?C?UIDIV)


LINK MAP OF MODULE:  .\Objects\speed_pwm (MAIN)


            TYPE    BASE      LENGTH    RELOCATION   SEGMENT NAME
            -----------------------------------------------------

            * * * * * * *   D A T A   M E M O R Y   * * * * * * *
            REG     0000H     0008H     ABSOLUTE     "REG BANK 0"
            DATA    0008H     0002H     UNIT         _DATA_GROUP_
            IDATA   000AH     0001H     UNIT         ?STACK

            * * * * * * *   C O D E   M E M O R Y   * * * * * * *
            CODE    0000H     0003H     ABSOLUTE     
            CODE    0003H     0090H     UNIT         ?PR?MAIN?MAIN
            CODE    0093H     0067H     UNIT         ?C?LIB_CODE
            CODE    00FAH     0020H     UNIT         ?PR?_DELAY_MS?KEY
            CODE    011AH     001FH     UNIT         ?PR?_PWM_SETDUTYPERCENT?PWM
            CODE    0139H     0019H     UNIT         ?PR?PWM_INIT?PWM
            CODE    0152H     0016H     UNIT         ?PR?KEY_SCAN?KEY
            CODE    0168H     000EH     UNIT         ?PR?_PWM_SETDUTY?PWM
            CODE    0176H     000CH     UNIT         ?C_C51STARTUP
            CODE    0182H     0004H     UNIT         ?PR?PWM_ENABLE?PWM
            CODE    0186H     0004H     UNIT         ?PR?PWM_DISABLE?PWM
            CODE    018AH     0003H     UNIT         ?PR?KEY_INIT?KEY



OVERLAY MAP OF MODULE:   .\Objects\speed_pwm (MAIN)


SEGMENT                               DATA_GROUP 
  +--> CALLED SEGMENT               START    LENGTH
---------------------------------------------------
?C_C51STARTUP                       -----    -----
  +--> ?PR?MAIN?MAIN

?PR?MAIN?MAIN                       0008H    0002H
  +--> ?PR?PWM_INIT?PWM
  +--> ?PR?KEY_INIT?KEY
  +--> ?PR?_PWM_SETDUTYPERCENT?PWM
  +--> ?PR?KEY_SCAN?KEY
BL51 BANKED LINKER/LOCATER V6.22.2.0                                                  06/17/2025  08:59:36  PAGE 2



?PR?_PWM_SETDUTYPERCENT?PWM         -----    -----
  +--> ?PR?_PWM_SETDUTY?PWM

?PR?KEY_SCAN?KEY                    -----    -----
  +--> ?PR?_DELAY_MS?KEY



UNRESOLVED EXTERNAL SYMBOLS:
   INITALLLEDRAMAPP
   LEDAPP
   LEDWORKGROUPSTR



SYMBOL TABLE OF MODULE:  .\Objects\speed_pwm (MAIN)

  VALUE           TYPE          NAME
  ----------------------------------

  -------         MODULE        MAIN
  C:0000H         SYMBOL        _ICE_DUMMY_
  D:0090H         PUBLIC        P1
  D:00B0H         PUBLIC        P3
  D:00C0H         PUBLIC        P4
  C:0003H         PUBLIC        main
  -------         PROC          MAIN
  -------         DO            
  D:0008H         SYMBOL        key_val
  D:0009H         SYMBOL        duty_level
  -------         ENDDO         
  C:0003H         LINE#         6
  C:0003H         LINE#         7
  C:0003H         LINE#         9
  C:0006H         LINE#         11
  C:0009H         LINE#         12
  C:000CH         LINE#         13
  C:000FH         LINE#         14
  C:0014H         LINE#         15
  C:0016H         LINE#         17
  C:0016H         LINE#         18
  C:0016H         LINE#         19
  C:001BH         LINE#         22
  C:001EH         LINE#         24
  C:0024H         LINE#         25
  C:0024H         LINE#         26
  C:002AH         LINE#         27
  C:0033H         LINE#         28
  C:0036H         LINE#         30
  C:003BH         LINE#         33
  C:0049H         LINE#         34
  C:004EH         LINE#         35
  C:0059H         LINE#         36
  C:005EH         LINE#         37
  C:0069H         LINE#         38
  C:006EH         LINE#         39
BL51 BANKED LINKER/LOCATER V6.22.2.0                                                  06/17/2025  08:59:36  PAGE 3


  C:0079H         LINE#         40
  C:007EH         LINE#         41
  C:0089H         LINE#         42
  C:008EH         LINE#         44
  C:0091H         LINE#         45
  C:0091H         LINE#         46
  -------         ENDPROC       MAIN
  -------         ENDMOD        MAIN

  -------         MODULE        PWM
  C:0000H         SYMBOL        _ICE_DUMMY_
  D:0090H         PUBLIC        P1
  C:011AH         PUBLIC        _PWM_SetDutyPercent
  D:00B0H         PUBLIC        P3
  D:00C0H         PUBLIC        P4
  C:0186H         PUBLIC        PWM_Disable
  C:0182H         PUBLIC        PWM_Enable
  C:0168H         PUBLIC        _PWM_SetDuty
  D:00D3H         PUBLIC        PWMD
  D:00D2H         PUBLIC        PWMP
  D:00D1H         PUBLIC        PWMCON
  C:0139H         PUBLIC        PWM_Init
  -------         PROC          PWM_INIT
  C:0139H         LINE#         5
  C:0139H         LINE#         6
  C:0139H         LINE#         8
  C:013CH         LINE#         9
  C:013FH         LINE#         12
  C:0142H         LINE#         13
  C:0145H         LINE#         14
  C:0148H         LINE#         15
  C:014BH         LINE#         17
  C:014EH         LINE#         18
  C:0151H         LINE#         19
  -------         ENDPROC       PWM_INIT
  -------         PROC          _PWM_SETDUTY
  D:0006H         SYMBOL        duty
  C:0168H         LINE#         22
  C:0168H         LINE#         23
  C:0168H         LINE#         24
  C:0171H         LINE#         25
  C:0173H         LINE#         26
  C:0175H         LINE#         27
  -------         ENDPROC       _PWM_SETDUTY
  -------         PROC          _PWM_SETDUTYPERCENT
  D:0007H         SYMBOL        pct
  -------         DO            
  D:0006H         SYMBOL        val
  -------         ENDDO         
  C:011AH         LINE#         30
  C:011AH         LINE#         31
  C:011AH         LINE#         33
  C:0126H         LINE#         35
  C:0136H         LINE#         36
  -------         ENDPROC       _PWM_SETDUTYPERCENT
  -------         PROC          PWM_ENABLE
  C:0182H         LINE#         41
BL51 BANKED LINKER/LOCATER V6.22.2.0                                                  06/17/2025  08:59:36  PAGE 4


  C:0182H         LINE#         42
  C:0182H         LINE#         43
  C:0185H         LINE#         44
  -------         ENDPROC       PWM_ENABLE
  -------         PROC          PWM_DISABLE
  C:0186H         LINE#         47
  C:0186H         LINE#         48
  C:0186H         LINE#         49
  C:0189H         LINE#         50
  -------         ENDPROC       PWM_DISABLE
  -------         ENDMOD        PWM

  -------         MODULE        KEY
  C:0000H         SYMBOL        _ICE_DUMMY_
  D:0090H         PUBLIC        P1
  D:00B0H         PUBLIC        P3
  D:00C0H         PUBLIC        P4
  C:0152H         PUBLIC        Key_Scan
  B:00B0H.3       PUBLIC        P3_3
  C:018AH         PUBLIC        Key_Init
  C:00FAH         PUBLIC        _delay_ms
  -------         PROC          KEY_INIT
  C:018AH         LINE#         4
  C:018AH         LINE#         5
  C:018AH         LINE#         7
  C:018CH         LINE#         8
  -------         ENDPROC       KEY_INIT
  -------         PROC          KEY_SCAN
  C:0152H         LINE#         11
  C:0152H         LINE#         12
  C:0152H         LINE#         13
  C:0155H         LINE#         14
  C:0155H         LINE#         16
  C:015CH         LINE#         17
  C:015FH         LINE#         18
  C:015FH         LINE#         19
  C:0162H         LINE#         20
  C:0165H         LINE#         21
  C:0165H         LINE#         22
  C:0165H         LINE#         23
  C:0167H         LINE#         24
  -------         ENDPROC       KEY_SCAN
  -------         PROC          _DELAY_MS
  D:0006H         SYMBOL        ms
  -------         DO            
  D:0004H         SYMBOL        i
  D:0002H         SYMBOL        j
  -------         ENDDO         
  C:00FAH         LINE#         27
  C:00FAH         LINE#         28
  C:00FAH         LINE#         30
  C:0104H         LINE#         31
  C:0119H         LINE#         32
  -------         ENDPROC       _DELAY_MS
  -------         ENDMOD        KEY

  -------         MODULE        ?C?IMUL
BL51 BANKED LINKER/LOCATER V6.22.2.0                                                  06/17/2025  08:59:36  PAGE 5


  C:0093H         PUBLIC        ?C?IMUL
  -------         ENDMOD        ?C?IMUL

  -------         MODULE        ?C?UIDIV
  C:00A5H         PUBLIC        ?C?UIDIV
  -------         ENDMOD        ?C?UIDIV

*** WARNING L16: UNCALLED SEGMENT, IGNORED FOR OVERLAY PROCESS
    SEGMENT: ?PR?PWM_ENABLE?PWM

*** WARNING L16: UNCALLED SEGMENT, IGNORED FOR OVERLAY PROCESS
    SEGMENT: ?PR?PWM_DISABLE?PWM

*** WARNING L1: UNRESOLVED EXTERNAL SYMBOL
    SYMBOL:  LEDWORKGROUPSTR
    MODULE:  .\Objects\main.obj (MAIN)

*** WARNING L1: UNRESOLVED EXTERNAL SYMBOL
    SYMBOL:  INITALLLEDRAMAPP
    MODULE:  .\Objects\main.obj (MAIN)

*** WARNING L1: UNRESOLVED EXTERNAL SYMBOL
    SYMBOL:  LEDAPP
    MODULE:  .\Objects\main.obj (MAIN)

*** WARNING L2: REFERENCE MADE TO UNRESOLVED EXTERNAL
    SYMBOL:  INITALLLEDRAMAPP
    MODULE:  .\Objects\main.obj (MAIN)
    ADDRESS: 000DH

*** WARNING L2: REFERENCE MADE TO UNRESOLVED EXTERNAL
    SYMBOL:  LEDAPP
    MODULE:  .\Objects\main.obj (MAIN)
    ADDRESS: 001CH

*** WARNING L2: REFERENCE MADE TO UNRESOLVED EXTERNAL
    SYMBOL:  LEDWORKGROUPSTR
    MODULE:  .\Objects\main.obj (MAIN)
    ADDRESS: 004AH

*** WARNING L2: REFERENCE MADE TO UNRESOLVED EXTERNAL
    SYMBOL:  LEDWORKGROUPSTR
    MODULE:  .\Objects\main.obj (MAIN)
    ADDRESS: 005AH

*** WARNING L2: REFERENCE MADE TO UNRESOLVED EXTERNAL
    SYMBOL:  LEDWORKGROUPSTR
    MODULE:  .\Objects\main.obj (MAIN)
    ADDRESS: 006AH

*** WARNING L2: REFERENCE MADE TO UNRESOLVED EXTERNAL
    SYMBOL:  LEDWORKGROUPSTR
    MODULE:  .\Objects\main.obj (MAIN)
    ADDRESS: 007AH

*** WARNING L2: REFERENCE MADE TO UNRESOLVED EXTERNAL
    SYMBOL:  LEDWORKGROUPSTR
BL51 BANKED LINKER/LOCATER V6.22.2.0                                                  06/17/2025  08:59:36  PAGE 6


    MODULE:  .\Objects\main.obj (MAIN)
    ADDRESS: 008AH

*** WARNING L2: REFERENCE MADE TO UNRESOLVED EXTERNAL
    SYMBOL:  LEDWORKGROUPSTR
    MODULE:  .\Objects\main.obj (MAIN)
    ADDRESS: 008FH

Program Size: data=11.0 xdata=0 code=397
LINK/LOCATE RUN COMPLETE.  13 WARNING(S),  0 ERROR(S)
