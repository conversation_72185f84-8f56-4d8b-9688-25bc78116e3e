/*==============================================================================
            Copyright(C) 2021-2029.  XXXXXX Tech. Co., Ltd.
--------------------------------------------------------------------------------
Project No.   : XXXXXX
File Name     : LEDAPP..H	
Description   : 
              ��    
              ��   
Notice        :
             1:
             2:

Author        :  
Start Date    :  
Release Date  :  
Approve Date  :
Version       : V1.0
CheckSum      : XXXXXXX
Function List :
             1:
             2:

RevisionHistory:
Rev#  CheckSum   Date     Author     Comments(Function+Date)
-----+--------+----------+---------+--------------------------------------------
0.0 2021/10/08 Author Just build the function
0.1
==============================================================================*/


//------------------------------------------------------------------------------
#ifndef LEDAPP_H
#define LEDAPP_H

#ifdef LEDAPP_C
   #define LEDAPP_EXT
#else
   #define LEDAPP_EXT extern
#endif

// 速度级别定义
#define D_SPEED_LEVEL_1    0x01
#define D_SPEED_LEVEL_2    0x02
#define D_SPEED_LEVEL_3    0x03
#define D_SPEED_LEVEL_4    0x04
#define D_SPEED_LEVEL_5    0x05
#define D_SPEED_LEVEL_6    0x06

// LED工作组结构体
typedef struct
{
    unsigned char LedWorkMode;
    // 其他可能的字段...
} LedWorkGroupType;

LEDAPP_EXT LedWorkGroupType LedWorkGroupStr;

// 函数声明
LEDAPP_EXT void InitAllLedRamApp(void);
LEDAPP_EXT void LedApp(void);

#endif
