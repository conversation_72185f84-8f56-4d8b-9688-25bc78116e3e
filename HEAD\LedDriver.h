/*==============================================================================
            Copyright(C) 2021-2029.  XXXXXX Tech. Co., Ltd.
--------------------------------------------------------------------------------
Project No.   : XXXXXX
File Name     : LEDDRIVER..H	
Description   : 
              ；    
              ；   
Notice        :
             1:
             2:

Author        :  
Start Date    :  
Release Date  :  
Approve Date  :
Version       : V1.0
CheckSum      : XXXXXXX
Function List :
             1:
             2:

RevisionHistory:
Rev#  CheckSum   Date     Author     Comments(Function+Date)
-----+--------+----------+---------+--------------------------------------------
0.0 2021/10/08 Author Just build the function
0.1
==============================================================================*/


//------------------------------------------------------------------------------
#ifndef  LEDDRIVER_H
#define  LEDDRIVER_H


#ifdef LEDDRIVER_C
   #define  LEDDRIVER_EXT     
#else 
   #define  LEDDRIVER_EXT   extern
#endif
//------------------------------------------------------------------------------

	 
 

	 
//led1  
#define LED1 				  				P1_7
#define LED1_IoOutSet()       {P1M0 &= Bin(01111111);P1M1 |= Bin(10000000);}
#define LED1_H()      		 		{LED1 = 1;}
#define LED1_L()     		 			{LED1 = 0;}

//led2  
#define LED2 				  				P1_6	 
#define LED2_IoOutSet()       {P1M0 &= Bin(10111111);P1M1 |= Bin(01000000);}	
#define LED2_H()      		    {LED2 = 1;}  
#define LED2_L()     		      {LED2 = 0;}

//led3  
#define LED3 				  				P1_5
#define LED3_IoOutSet()       {P1M0 &= Bin(11011111);P1M1 |= Bin(00100000);}
#define LED3_H()      		 		{LED3 = 1;}
#define LED3_L()     		 			{LED3 = 0;}

//led4  
#define LED4 				  				P1_4	 
#define LED4_IoOutSet()       {P1M0 &= Bin(11101111);P1M1 |= Bin(00010000);}	
#define LED4_H()      		    {LED4 = 1;}  
#define LED4_L()     		      {LED4 = 0;}

//led5  
#define LED5 				  				P1_3
#define LED5_IoOutSet()       {P1M0 &= Bin(11110111);P1M1 |= Bin(00001000);}
#define LED5_H()      		 		{LED5 = 1;}
#define LED5_L()     		 			{LED5 = 0;}

//led6  
#define LED6 				  				P1_2	 
#define LED6_IoOutSet()       {P1M0 &= Bin(11111011);P1M1 |= Bin(00000100);}	
#define LED6_H()      		    {LED6 = 1;}  
#define LED6_L()     		      {LED6 = 0;}



LEDDRIVER_EXT void InitAllLedDriverRam(void);

LEDDRIVER_EXT void InitAllLedOff(void);

LEDDRIVER_EXT void LED1_WorkOff(void);
LEDDRIVER_EXT void LED1_WorkOn(void);
//LEDDRIVER_EXT void LED1_WorkFlash(void);

LEDDRIVER_EXT void LED2_WorkOff(void);
LEDDRIVER_EXT void LED2_WorkOn(void);
//LEDDRIVER_EXT void LED2_WorkFlash(void);

LEDDRIVER_EXT void LED3_WorkOff(void);
LEDDRIVER_EXT void LED3_WorkOn(void);
//LEDDRIVER_EXT void LED3_WorkFlash(void);

LEDDRIVER_EXT void LED4_WorkOff(void);
LEDDRIVER_EXT void LED4_WorkOn(void);
//LEDDRIVER_EXT void LED4_WorkFlash(void);

LEDDRIVER_EXT void LED5_WorkOff(void);
LEDDRIVER_EXT void LED5_WorkOn(void);
//LEDDRIVER_EXT void LED5_WorkFlash(void);

LEDDRIVER_EXT void LED6_WorkOff(void);
LEDDRIVER_EXT void LED6_WorkOn(void);
//LEDDRIVER_EXT void LED6_WorkFlash(void);

#endif