#include ".\head\Config.h"
#include ".\head\PWM.h"
#include ".\head\Key.h"
#include ".\head\LedApp.h"  // 包含LED应用头文件

void main(void)
{
    unsigned char key_val;
    unsigned char duty_level = 12;  // 初始值设为12%

    PWM_Init();
    Key_Init();
    InitAllLedRamApp();  // 初始化LED
    PWM_SetDutyPercent(duty_level);  // 初始为12%
    LedWorkGroupStr.LedWorkMode = D_SPEED_LEVEL_1;  // 初始LED显示为1级

    while (1)
    {
        key_val = Key_Scan();
        
        // 持续更新LED显示
        LedApp();

        if (key_val == KEY1_PRESS)
        {
            duty_level += 15;
            if (duty_level > 90)
                duty_level = 12;

            PWM_SetDutyPercent(duty_level);  // 设置占空比
            
            // 根据占空比级别设置LED显示
            if (duty_level <= 15)
                LedWorkGroupStr.LedWorkMode = D_SPEED_LEVEL_1;
            else if (duty_level <= 30)
                LedWorkGroupStr.LedWorkMode = D_SPEED_LEVEL_2;
            else if (duty_level <= 45)
                LedWorkGroupStr.LedWorkMode = D_SPEED_LEVEL_3;
            else if (duty_level <= 60)
                LedWorkGroupStr.LedWorkMode = D_SPEED_LEVEL_4;
            else if (duty_level <= 75)
                LedWorkGroupStr.LedWorkMode = D_SPEED_LEVEL_5;
            else
                LedWorkGroupStr.LedWorkMode = D_SPEED_LEVEL_6;
        }
    }
}
